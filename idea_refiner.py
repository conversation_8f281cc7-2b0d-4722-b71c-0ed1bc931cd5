#!/usr/bin/env python3
"""
Idea Refiner Script

This script takes a list of high-level ideas and refines them into comprehensive
JIRA ticket descriptions using knowledge from past tickets, project structure,
and LLM assistance.

The script analyzes:
- Past JIRA tickets and their patterns
- Current project structure (from tree.log)
- Git commit history
- File changes associated with similar tickets

It generates detailed ticket descriptions with:
- Business value
- Acceptance criteria
- Open questions
- Suggested design approach
- Files likely to be modified
- Impact assessment
"""

import argparse
import json
import logging
import re
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from collections import defaultdict, Counter

# Import MLX components for LLM integration
try:
    from mlx_lm import load, generate
    from mlx_lm.sample_utils import make_sampler
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    logging.warning("MLX not available - LLM features will be disabled")

# Model presets from the translation script
PRESETS = {
    "gemma3": "/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
    "qwen3": "/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
    "alma13": "/Users/<USER>/.lmstudio/models/mlx-community/ALMA-13B-R-4bit-mlx",
}

@dataclass
class ProjectKnowledge:
    """Container for all project knowledge extracted from input files."""
    tickets: List[Dict]
    enriched_tickets: List[Dict]
    project_structure: List[str]
    git_commits: List[str]
    
    # Derived knowledge
    common_patterns: Dict[str, List[str]]
    file_change_patterns: Dict[str, List[str]]
    business_domains: Set[str]
    technical_components: Set[str]


@dataclass
class RefinedIdea:
    """Container for a refined idea with all generated content."""
    original_idea: str
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    related_tickets: List[str]


class KnowledgeExtractor:
    """Extracts and analyzes knowledge from project files."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_knowledge(self, jira_path: Path, enriched_path: Path, 
                         tree_path: Path, git_path: Path) -> ProjectKnowledge:
        """Extract all knowledge from input files."""
        self.logger.info("Extracting project knowledge...")
        
        # Load raw data
        tickets = self._load_json(jira_path)
        enriched_tickets = self._load_json(enriched_path)
        project_structure = self._load_text_lines(tree_path)
        git_commits = self._load_text_lines(git_path)
        
        # Analyze patterns
        common_patterns = self._extract_ticket_patterns(tickets)
        file_change_patterns = self._extract_file_patterns(enriched_tickets)
        business_domains = self._extract_business_domains(tickets)
        technical_components = self._extract_technical_components(project_structure)
        
        return ProjectKnowledge(
            tickets=tickets,
            enriched_tickets=enriched_tickets,
            project_structure=project_structure,
            git_commits=git_commits,
            common_patterns=common_patterns,
            file_change_patterns=file_change_patterns,
            business_domains=business_domains,
            technical_components=technical_components
        )
    
    def _load_json(self, path: Path) -> List[Dict]:
        """Load JSON file safely."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []
    
    def _load_text_lines(self, path: Path) -> List[str]:
        """Load text file as lines."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []
    
    def _extract_ticket_patterns(self, tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract common patterns from ticket descriptions."""
        patterns = defaultdict(list)
        
        for ticket in tickets:
            desc = ticket.get('description', '')
            title = ticket.get('title', '')
            
            # Extract business value patterns
            if '*Business Value:*' in desc:
                bv_match = re.search(r'\*Business Value:\*\s*([^*]+)', desc)
                if bv_match:
                    patterns['business_value'].append(bv_match.group(1).strip())
            
            # Extract acceptance criteria patterns
            if '*Acceptance Criteria:*' in desc:
                ac_match = re.search(r'\*Acceptance Criteria:\*\s*([^*]+)', desc)
                if ac_match:
                    patterns['acceptance_criteria'].append(ac_match.group(1).strip())
            
            # Extract impact patterns
            if '*Impact and notifications:*' in desc:
                impact_match = re.search(r'\*Impact and notifications:\*\s*([^*]+)', desc)
                if impact_match:
                    patterns['impact'].append(impact_match.group(1).strip())
        
        return dict(patterns)
    
    def _extract_file_patterns(self, enriched_tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract file change patterns by ticket type."""
        patterns = defaultdict(list)
        
        for ticket in enriched_tickets:
            title = ticket.get('title', '').lower()
            files_changed = ticket.get('files_changed', [])
            
            if not files_changed:
                continue
            
            # Categorize by ticket type
            if any(word in title for word in ['auth', 'authorization']):
                patterns['auth'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['clearing', 'settlement']):
                patterns['clearing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['api', 'endpoint']):
                patterns['api'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['test', 'testing']):
                patterns['testing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['config', 'configuration']):
                patterns['config'].extend([f['file_path'] for f in files_changed])
        
        return dict(patterns)
    
    def _extract_business_domains(self, tickets: List[Dict]) -> Set[str]:
        """Extract business domains from ticket content."""
        domains = set()
        
        for ticket in tickets:
            title = ticket.get('title', '').lower()
            desc = ticket.get('description', '').lower()
            content = f"{title} {desc}"
            
            # Common business domains in payment processing
            if any(word in content for word in ['visa', 'mastercard', 'mc']):
                domains.add('card_schemes')
            if any(word in content for word in ['amex', 'american express']):
                domains.add('amex')
            if any(word in content for word in ['auth', 'authorization']):
                domains.add('authorization')
            if any(word in content for word in ['clearing', 'settlement']):
                domains.add('clearing')
            if any(word in content for word in ['fraud', 'risk']):
                domains.add('fraud_prevention')
            if any(word in content for word in ['terminal', 'pos']):
                domains.add('terminal_management')
            if any(word in content for word in ['merchant', 'customer']):
                domains.add('merchant_services')
        
        return domains
    
    def _extract_technical_components(self, project_structure: List[str]) -> Set[str]:
        """Extract technical components from project structure."""
        components = set()
        
        for line in project_structure:
            if 'auth-' in line:
                components.add('auth_services')
            if 'clearing-' in line:
                components.add('clearing_services')
            if 'helm' in line:
                components.add('kubernetes')
            if 'config' in line:
                components.add('configuration')
            if 'test' in line:
                components.add('testing')
            if 'internal/' in line:
                components.add('internal_apis')
            if 'cmd/' in line:
                components.add('applications')
        
        return components


class LLMRefiner:
    """Uses LLM to refine ideas into comprehensive ticket descriptions."""
    
    def __init__(self, model_preset: str = "qwen3"):
        self.logger = logging.getLogger(__name__)
        self.model_preset = model_preset
        
        if not MLX_AVAILABLE:
            self.logger.warning("MLX not available - using template-based refinement")
            self.model = None
            self.tokenizer = None
            return
        
        try:
            model_path = PRESETS[model_preset]
            self.logger.info(f"Loading model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.logger.info("Model loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            self.model = None
            self.tokenizer = None
    
    def refine_idea(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Refine a single idea into a comprehensive ticket description."""
        self.logger.info(f"Refining idea: {idea}")
        
        if self.model is None:
            return self._template_based_refinement(idea, knowledge)
        else:
            return self._llm_based_refinement(idea, knowledge)
    
    def _template_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Fallback template-based refinement when LLM is not available."""
        self.logger.info("Using template-based refinement")
        
        # Generate title
        title = idea.strip().rstrip('.')
        if len(title) > 80:
            title = title[:77] + "..."
        
        # Analyze idea for domain and components
        idea_lower = idea.lower()
        relevant_domain = self._identify_domain(idea_lower, knowledge.business_domains)
        relevant_components = self._identify_components(idea_lower, knowledge.technical_components)
        
        # Generate sections
        business_value = self._generate_business_value(idea, relevant_domain)
        acceptance_criteria = self._generate_acceptance_criteria(idea, relevant_domain)
        open_questions = self._generate_open_questions(idea, relevant_domain)
        suggested_design = self._generate_design_suggestions(idea, relevant_components)
        likely_files = self._predict_file_changes(idea, knowledge.file_change_patterns)
        impact_assessment = self._generate_impact_assessment(idea, relevant_components)
        related_tickets = self._find_related_tickets(idea, knowledge.tickets)
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions, 
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=likely_files,
            impact_assessment=impact_assessment,
            related_tickets=related_tickets
        )
    
    def _llm_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """LLM-based refinement using the loaded model."""
        self.logger.info("Using LLM-based refinement")
        
        # Build context from knowledge
        context = self._build_llm_context(knowledge)
        
        # Create prompt
        prompt = self._create_refinement_prompt(idea, context)
        
        try:
            # Generate response
            sampler = make_sampler(temp=0.3, top_p=0.9)
            response = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=2048,
                sampler=sampler,
                verbose=False
            )
            
            # Parse LLM response
            return self._parse_llm_response(idea, response)
            
        except Exception as e:
            self.logger.error(f"LLM refinement failed: {e}")
            return self._template_based_refinement(idea, knowledge)
    
    def _build_llm_context(self, knowledge: ProjectKnowledge) -> str:
        """Build context string for LLM from project knowledge."""
        context_parts = []
        
        # Add sample ticket patterns
        if knowledge.common_patterns.get('business_value'):
            context_parts.append("Example Business Values:")
            for bv in knowledge.common_patterns['business_value'][:3]:
                context_parts.append(f"- {bv}")
        
        # Add technical components
        if knowledge.technical_components:
            context_parts.append(f"\nTechnical Components: {', '.join(list(knowledge.technical_components)[:10])}")
        
        # Add business domains
        if knowledge.business_domains:
            context_parts.append(f"Business Domains: {', '.join(list(knowledge.business_domains))}")
        
        return "\n".join(context_parts)
    
    def _create_refinement_prompt(self, idea: str, context: str) -> str:
        """Create prompt for LLM refinement."""
        return f"""You are a senior technical product manager for a payment processing system. 
Your task is to refine a high-level idea into a comprehensive JIRA ticket description.

Project Context:
{context}

High-level idea to refine:
"{idea}"

Please provide a detailed ticket description with the following sections:
1. Title (concise, actionable)
2. Business Value (why this matters)
3. Acceptance Criteria (specific, testable requirements)
4. Open Questions (technical and business questions to resolve)
5. Suggested Design (high-level technical approach)
6. Impact Assessment (risks, dependencies, notifications needed)

Format your response as structured text with clear section headers.
"""
    
    def _parse_llm_response(self, idea: str, response: str) -> RefinedIdea:
        """Parse LLM response into RefinedIdea structure."""
        # This is a simplified parser - in practice you'd want more robust parsing
        sections = {}
        current_section = None
        current_content = []
        
        for line in response.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # Check for section headers
            if any(header in line.lower() for header in ['title:', 'business value:', 'acceptance criteria:', 'open questions:', 'suggested design:', 'impact assessment:']):
                if current_section:
                    sections[current_section] = '\n'.join(current_content)
                current_section = line.lower().split(':')[0].strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Add final section
        if current_section:
            sections[current_section] = '\n'.join(current_content)
        
        # Extract structured data
        title = sections.get('title', idea)
        business_value = sections.get('business value', 'To be determined')
        acceptance_criteria = [sections.get('acceptance criteria', 'To be defined')]
        open_questions = [sections.get('open questions', 'To be identified')]
        suggested_design = sections.get('suggested design', 'To be designed')
        impact_assessment = sections.get('impact assessment', 'To be assessed')
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions,
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=[],  # Would need additional analysis
            impact_assessment=impact_assessment,
            related_tickets=[]  # Would need additional analysis
        )
    
    def _identify_domain(self, idea_lower: str, domains: Set[str]) -> str:
        """Identify the most relevant business domain for the idea."""
        domain_keywords = {
            'authorization': ['auth', 'authorization', 'approve', 'decline'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'fraud_prevention': ['fraud', 'risk', 'security', 'detect'],
            'card_schemes': ['visa', 'mastercard', 'mc', 'scheme'],
            'merchant_services': ['merchant', 'customer', 'dashboard'],
            'terminal_management': ['terminal', 'pos', 'device']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                return domain
        
        return 'general'

    def _identify_components(self, idea_lower: str, components: Set[str]) -> List[str]:
        """Identify relevant technical components for the idea."""
        component_keywords = {
            'auth_services': ['auth', 'authorization', 'approve'],
            'clearing_services': ['clearing', 'settlement'],
            'kubernetes': ['deploy', 'scale', 'infrastructure'],
            'configuration': ['config', 'setting', 'parameter'],
            'testing': ['test', 'validation', 'verify'],
            'internal_apis': ['api', 'endpoint', 'service'],
            'applications': ['app', 'application', 'service']
        }

        relevant = []
        for component, keywords in component_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                relevant.append(component)

        return relevant

    def _generate_business_value(self, idea: str, domain: str) -> str:
        """Generate specific business value statement based on idea content."""
        idea_lower = idea.lower()

        # Analyze idea for specific business value
        if 'block' in idea_lower and any(scheme in idea_lower for scheme in ['mc', 'mastercard', 'jcb']):
            return ("Ensures compliance with MC and JCB scheme rules regarding recurring transactions. "
                   "Reduces risk of scheme fines and penalties for non-compliant recurring auth processing. "
                   "Protects merchants from unexpected recurring charges that could lead to chargebacks.")

        elif 'kafka' in idea_lower and 'sqs' in idea_lower:
            return ("Improves clearing instruction processing reliability and scalability. "
                   "Reduces operational costs by consolidating on single messaging platform. "
                   "Enables better monitoring and troubleshooting of clearing message flows. "
                   "Supports higher throughput for growing transaction volumes.")

        elif 'amex' in idea_lower and 'reversal' in idea_lower and 'datetime' in idea_lower:
            return ("Ensures Amex compliance and reduces risk of reversal rejections. "
                   "Improves merchant experience by reducing failed reversal attempts. "
                   "Maintains strong relationship with Amex by meeting their technical requirements. "
                   "Reduces support burden from reversal-related issues.")

        elif 'migration' in idea_lower and 'squash' in idea_lower:
            return ("Reduces deployment complexity and time for new environments. "
                   "Improves developer productivity by simplifying database setup. "
                   "Reduces risk of migration-related issues in production deployments. "
                   "Enables faster disaster recovery and environment provisioning.")

        else:
            # More specific generic value based on domain
            domain_values = {
                'authorization': 'Improves payment authorization accuracy and reduces false declines',
                'clearing': 'Streamlines settlement processes and reduces operational overhead',
                'fraud_prevention': 'Reduces fraud losses and improves customer trust',
                'card_schemes': 'Ensures compliance and improves scheme relationships',
                'merchant_services': 'Enhances merchant experience and reduces support burden',
                'terminal_management': 'Improves terminal efficiency and reduces downtime',
                'general': 'Enhances system capabilities and operational efficiency'
            }

            return domain_values.get(domain, domain_values['general'])

    def _generate_acceptance_criteria(self, idea: str, domain: str) -> List[str]:
        """Generate specific acceptance criteria based on idea content and domain."""
        idea_lower = idea.lower()
        criteria = []

        # Analyze idea for specific requirements
        if 'block' in idea_lower and any(scheme in idea_lower for scheme in ['mc', 'mastercard', 'jcb']):
            criteria.extend([
                "MC and JCB recurring authorization requests are rejected with appropriate response codes",
                "Existing non-recurring auth flows remain unaffected",
                "Component tests validate rejection of recurring auths for MC/JCB schemes",
                "Visa and Amex recurring auths continue to work as before",
                "Response codes follow scheme specifications (RC=05 for MC, appropriate JCB code)",
                "Audit logs capture blocked recurring auth attempts with scheme and reason"
            ])
        elif 'kafka' in idea_lower and 'sqs' in idea_lower:
            criteria.extend([
                "All clearing instructions route through Kafka topics instead of SQS queues",
                "Message ordering preserved during migration from SQS to Kafka",
                "Dead letter handling implemented for failed Kafka messages",
                "Kafka consumer groups configured for high availability",
                "Message schemas validated and backward compatible",
                "Performance metrics show no degradation in clearing instruction throughput",
                "SQS queues can be safely decommissioned after successful migration"
            ])
        elif 'amex' in idea_lower and 'reversal' in idea_lower and 'datetime' in idea_lower:
            criteria.extend([
                "Reversal DE7 (transmission date/time) matches original auth DE7 exactly",
                "Terminal local time zone handling implemented correctly",
                "Amex certification test cases pass with correct datetime formatting",
                "Component tests validate datetime consistency between auth and reversal",
                "No impact on Visa/MC reversal datetime handling",
                "Amex simulator responds correctly to new datetime format"
            ])
        elif 'migration' in idea_lower and 'squash' in idea_lower:
            criteria.extend([
                "All existing migrations consolidated into single comprehensive migration",
                "Database schema matches current production state exactly",
                "Migration rollback strategy tested and documented",
                "All environments (dev/stg/prod) can apply the consolidated migration",
                "Foreign key constraints and indexes preserved correctly",
                "Data integrity verified after migration consolidation",
                "Migration execution time reduced compared to running all individual migrations"
            ])
        else:
            # Generic but more specific criteria
            criteria.extend([
                "Implementation follows existing code patterns and architecture",
                "Unit tests achieve >90% code coverage for new/modified code",
                "Integration tests validate end-to-end functionality"
            ])

        # Add common payment processing criteria
        if any(word in idea_lower for word in ['auth', 'authorization', 'clearing', 'reversal']):
            criteria.extend([
                "No impact on payment processing SLAs (auth <3s, clearing <30s)",
                "Monitoring and alerting configured for new functionality",
                "Gradual rollout plan with feature flags for safe deployment"
            ])

        return criteria

    def _generate_open_questions(self, idea: str, domain: str) -> List[str]:
        """Generate specific open questions based on idea content."""
        idea_lower = idea.lower()
        questions = []

        # Analyze idea for specific questions
        if 'block' in idea_lower and any(scheme in idea_lower for scheme in ['mc', 'mastercard', 'jcb']):
            questions.extend([
                "What specific response codes should we return for blocked MC recurring auths (RC=05 or RC=57)?",
                "How do we identify recurring auths - by processing code, merchant category, or other DE fields?",
                "Should we block all recurring auths or only specific types (e.g., subscription vs installment)?",
                "Do we need to notify merchants before implementing this blocking?",
                "What's the timeline for JCB certification if we change their auth behavior?",
                "Should this be configurable per merchant or a global rule?"
            ])
        elif 'kafka' in idea_lower and 'sqs' in idea_lower:
            questions.extend([
                "Which Kafka cluster should we use (existing or new dedicated cluster)?",
                "What's the migration strategy - big bang or gradual topic-by-topic?",
                "How do we handle message ordering requirements during transition?",
                "What's the retention policy for clearing instruction messages in Kafka?",
                "Do we need schema registry for message validation?",
                "How will this affect clearing team's message consumption patterns?",
                "What's the rollback plan if Kafka has issues during clearing windows?"
            ])
        elif 'amex' in idea_lower and 'reversal' in idea_lower and 'datetime' in idea_lower:
            questions.extend([
                "Should we store terminal timezone info or derive it from merchant location?",
                "How do we handle daylight saving time transitions?",
                "What if original auth DE7 is missing or corrupted?",
                "Does this apply to all Amex reversal types (full, partial, timeout)?",
                "Do we need to update our Amex simulator to match this behavior?",
                "What's Amex's certification timeline for this change?"
            ])
        elif 'migration' in idea_lower and 'squash' in idea_lower:
            questions.extend([
                "Which migrations should be included in the consolidation (all or from specific date)?",
                "How do we handle environment-specific migrations (dev-only, prod-only)?",
                "What's the strategy for databases that have partial migration history?",
                "Should we preserve migration history for audit purposes?",
                "How do we test the consolidated migration against production-like data volumes?",
                "What's the downtime window required for production migration?"
            ])
        else:
            # More specific generic questions
            questions.extend([
                "What's the expected impact on system performance and throughput?",
                "Are there scheme certification requirements for this change?",
                "What's the rollback strategy if issues are discovered post-deployment?"
            ])

        # Add common payment processing questions
        if any(word in idea_lower for word in ['auth', 'authorization']):
            questions.append("How will this affect our authorization success rates and false decline rates?")

        if any(word in idea_lower for word in ['clearing', 'settlement']):
            questions.append("What's the impact on daily clearing cut-off times and settlement windows?")

        return questions

    def _generate_design_suggestions(self, idea: str, components: List[str]) -> str:
        """Generate specific design suggestions based on idea content."""
        idea_lower = idea.lower()
        suggestions = []

        # Analyze idea for specific design approaches
        if 'block' in idea_lower and any(scheme in idea_lower for scheme in ['mc', 'mastercard', 'jcb']):
            suggestions.extend([
                "Add validation in auth request processing to check for recurring transaction indicators",
                "Implement scheme-specific logic in MC and JCB auth handlers",
                "Use processing code (DE3) and merchant category code (DE18) to identify recurring auths",
                "Add configuration flags to enable/disable blocking per scheme",
                "Update auth response builders to return appropriate decline codes",
                "Ensure Visa and Amex auth flows remain unchanged"
            ])

        elif 'kafka' in idea_lower and 'sqs' in idea_lower:
            suggestions.extend([
                "Create new Kafka topics with appropriate partitioning strategy",
                "Implement dual-write pattern during migration (write to both SQS and Kafka)",
                "Use Kafka Connect for reliable message delivery and error handling",
                "Implement consumer groups with proper offset management",
                "Add message serialization/deserialization with schema validation",
                "Create monitoring dashboards for Kafka topic health and consumer lag",
                "Plan phased migration: producer switch first, then consumer switch"
            ])

        elif 'amex' in idea_lower and 'reversal' in idea_lower and 'datetime' in idea_lower:
            suggestions.extend([
                "Modify Amex reversal request builder to use original auth DE7 timestamp",
                "Store original auth DE7 in database for reversal reference",
                "Add timezone handling logic for terminal local time conversion",
                "Update Amex scheme converter to format datetime according to Amex specs",
                "Ensure reversal DE7 exactly matches original auth DE7 format",
                "Add validation to prevent reversal if original auth DE7 is missing"
            ])

        elif 'migration' in idea_lower and 'squash' in idea_lower:
            suggestions.extend([
                "Create script to analyze and consolidate existing migration files",
                "Generate single comprehensive migration with proper ordering",
                "Implement migration version tracking to handle partial applications",
                "Add data validation checks before and after consolidated migration",
                "Create separate migrations for schema vs data changes",
                "Implement rollback scripts for each major schema change",
                "Test consolidated migration against production data snapshots"
            ])

        else:
            suggestions.append("Follow existing architectural patterns and coding standards")

        # Add common design considerations
        suggestions.extend([
            "Implement comprehensive logging with correlation IDs for traceability",
            "Add metrics and monitoring for new functionality",
            "Use feature flags for safe gradual rollout",
            "Ensure backward compatibility during transition period"
        ])

        return ". ".join(suggestions) + "."

    def _predict_file_changes(self, idea: str, file_patterns: Dict[str, List[str]]) -> List[str]:
        """Predict likely file changes based on idea content."""
        idea_lower = idea.lower()
        likely_files = []

        # Map idea keywords to file pattern categories
        if any(word in idea_lower for word in ['auth', 'authorization']):
            likely_files.extend(file_patterns.get('auth', [])[:5])
        if any(word in idea_lower for word in ['clearing', 'settlement']):
            likely_files.extend(file_patterns.get('clearing', [])[:5])
        if any(word in idea_lower for word in ['api', 'endpoint']):
            likely_files.extend(file_patterns.get('api', [])[:5])
        if any(word in idea_lower for word in ['test', 'testing']):
            likely_files.extend(file_patterns.get('testing', [])[:5])
        if any(word in idea_lower for word in ['config', 'configuration']):
            likely_files.extend(file_patterns.get('config', [])[:5])

        # Remove duplicates while preserving order
        seen = set()
        unique_files = []
        for file in likely_files:
            if file not in seen:
                seen.add(file)
                unique_files.append(file)

        return unique_files[:10]  # Limit to top 10 predictions

    def _generate_impact_assessment(self, idea: str, components: List[str]) -> str:
        """Generate impact assessment."""
        impacts = []

        if any(comp in components for comp in ['auth_services', 'clearing_services']):
            impacts.append("Could affect critical payment processing - requires careful testing")

        if 'kubernetes' in components:
            impacts.append("May require infrastructure changes and deployment coordination")

        if not impacts:
            impacts.append("Low to medium impact expected")

        impacts.extend([
            "Notify relevant teams before implementation",
            "Plan for monitoring during rollout",
            "Ensure rollback procedures are in place"
        ])

        return ". ".join(impacts) + "."

    def _find_related_tickets(self, idea: str, tickets: List[Dict]) -> List[str]:
        """Find related tickets based on content similarity."""
        idea_words = set(idea.lower().split())
        related = []

        for ticket in tickets[:100]:  # Limit search for performance
            title = ticket.get('title', '').lower()
            title_words = set(title.split())

            # Simple word overlap scoring
            overlap = len(idea_words.intersection(title_words))
            if overlap >= 2:  # At least 2 words in common
                related.append(f"{ticket.get('key', 'UNKNOWN')}: {ticket.get('title', 'No title')}")

        return related[:5]  # Return top 5 related tickets

    def _build_description(self, business_value: str, acceptance_criteria: List[str],
                          open_questions: List[str], suggested_design: str,
                          impact_assessment: str) -> str:
        """Build the complete ticket description."""
        description_parts = [
            f"*Business Value:*\n{business_value}\n",
            f"*Acceptance Criteria:*"
        ]

        for i, criteria in enumerate(acceptance_criteria, 1):
            description_parts.append(f"* {criteria}")

        description_parts.extend([
            f"\n*Open Questions:*"
        ])

        for i, question in enumerate(open_questions, 1):
            description_parts.append(f"* {question}")

        description_parts.extend([
            f"\n*Suggested Design:*\n{suggested_design}\n",
            f"*Impact and Notifications:*\n{impact_assessment}"
        ])

        return "\n".join(description_parts)


class IdeaRefinerApp:
    """Main application class that orchestrates the idea refinement process."""

    def __init__(self, model_preset: str = "qwen3"):
        self.logger = logging.getLogger(__name__)
        self.knowledge_extractor = KnowledgeExtractor()
        self.llm_refiner = LLMRefiner(model_preset)

    def refine_ideas_from_file(self, ideas_path: Path, jira_path: Path,
                              enriched_path: Path, tree_path: Path,
                              git_path: Path, output_dir: Path) -> None:
        """Refine all ideas from a file and generate ticket descriptions."""

        # Extract project knowledge
        knowledge = self.knowledge_extractor.extract_knowledge(
            jira_path, enriched_path, tree_path, git_path
        )

        # Load ideas
        ideas = self._load_ideas(ideas_path)
        self.logger.info(f"Loaded {len(ideas)} ideas to refine")

        # Create output directory
        output_dir.mkdir(parents=True, exist_ok=True)

        # Process each idea
        for i, idea in enumerate(ideas, 1):
            self.logger.info(f"Processing idea {i}/{len(ideas)}: {idea[:50]}...")

            try:
                refined = self.llm_refiner.refine_idea(idea, knowledge)

                # Generate output filename
                safe_title = self._make_safe_filename(refined.title)
                output_file = output_dir / f"ticket_{i:02d}_{safe_title}.txt"

                # Write ticket description
                self._write_ticket_file(refined, output_file)

                self.logger.info(f"Generated ticket: {output_file}")

            except Exception as e:
                self.logger.error(f"Failed to process idea {i}: {e}")
                continue

        self.logger.info(f"Completed processing {len(ideas)} ideas")

    def _load_ideas(self, ideas_path: Path) -> List[str]:
        """Load ideas from text file."""
        try:
            with open(ideas_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]

            # Handle numbered lists
            ideas = []
            for line in lines:
                # Remove leading numbers and dots
                clean_line = re.sub(r'^\d+\.\s*', '', line)
                if clean_line:
                    ideas.append(clean_line)

            return ideas

        except Exception as e:
            self.logger.error(f"Failed to load ideas from {ideas_path}: {e}")
            return []

    def _make_safe_filename(self, title: str) -> str:
        """Convert title to safe filename."""
        # Remove/replace unsafe characters
        safe = re.sub(r'[^\w\s-]', '', title)
        safe = re.sub(r'[-\s]+', '_', safe)
        return safe.lower()[:50]  # Limit length

    def _write_ticket_file(self, refined: RefinedIdea, output_file: Path) -> None:
        """Write refined idea to ticket file."""
        content_parts = [
            f"# {refined.title}",
            "",
            f"**Original Idea:** {refined.original_idea}",
            "",
            refined.description,
            ""
        ]

        if refined.likely_files:
            content_parts.extend([
                "*Likely Files to Modify:*",
                ""
            ])
            for file_path in refined.likely_files:
                content_parts.append(f"* {file_path}")
            content_parts.append("")

        if refined.related_tickets:
            content_parts.extend([
                "*Related Tickets:*",
                ""
            ])
            for ticket in refined.related_tickets:
                content_parts.append(f"* {ticket}")
            content_parts.append("")

        content_parts.extend([
            "*Refined By:* Idea Refiner Script",
            "",
            f"*Dependencies:* To be determined",
            "",
            f"*Out of Scope:* To be determined"
        ])

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(content_parts))


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Refine high-level ideas into comprehensive JIRA ticket descriptions",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--ideas",
        type=Path,
        required=True,
        help="Text file containing ideas (one per line or numbered list)"
    )

    parser.add_argument(
        "--jira_tickets",
        type=Path,
        required=True,
        help="JSON file with historical JIRA tickets"
    )

    parser.add_argument(
        "--enriched_tickets",
        type=Path,
        required=True,
        help="JSON file with enriched JIRA tickets (with file changes)"
    )

    parser.add_argument(
        "--tree_log",
        type=Path,
        required=True,
        help="Text file with project structure (tree output)"
    )

    parser.add_argument(
        "--git_log",
        type=Path,
        required=True,
        help="Text file with git commit history"
    )

    parser.add_argument(
        "--output_dir",
        type=Path,
        default=Path("refined_tickets"),
        help="Directory to write refined ticket files"
    )

    parser.add_argument(
        "--model",
        choices=list(PRESETS.keys()),
        default="qwen3",
        help="LLM model preset to use for refinement"
    )

    parser.add_argument(
        "--log_level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )

    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()

    # Set up logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    logger = logging.getLogger(__name__)

    # Validate input files
    for file_path in [args.ideas, args.jira_tickets, args.enriched_tickets,
                      args.tree_log, args.git_log]:
        if not file_path.exists():
            logger.error(f"Input file not found: {file_path}")
            sys.exit(1)

    # Check MLX availability if using LLM
    if not MLX_AVAILABLE:
        logger.warning("MLX not available - using template-based refinement only")

    try:
        # Create and run the refiner
        app = IdeaRefinerApp(model_preset=args.model)
        app.refine_ideas_from_file(
            args.ideas, args.jira_tickets, args.enriched_tickets,
            args.tree_log, args.git_log, args.output_dir
        )

        logger.info(f"Refinement complete. Check output in: {args.output_dir}")

    except Exception as e:
        logger.error(f"Refinement failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
