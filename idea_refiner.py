#!/usr/bin/env python3
"""
Idea Refiner Script

This script takes a list of high-level ideas and refines them into comprehensive
JIRA ticket descriptions using knowledge from past tickets, project structure,
and LLM assistance.

The script analyzes:
- Past JIRA tickets and their patterns
- Current project structure (from tree.log)
- Git commit history
- File changes associated with similar tickets

It generates detailed ticket descriptions with:
- Business value
- Acceptance criteria
- Open questions
- Suggested design approach
- Files likely to be modified
- Impact assessment
"""

import argparse
import json
import logging
import re
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from collections import defaultdict, Counter

# Import MLX components for LLM integration
try:
    from mlx_lm import load, generate
    from mlx_lm.sample_utils import make_sampler
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    logging.warning("MLX not available - LLM features will be disabled")

# Model presets from the translation script
PRESETS = {
    "gemma3": "/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
    "qwen3": "/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
    "alma13": "/Users/<USER>/.lmstudio/models/mlx-community/ALMA-13B-R-4bit-mlx",
}

@dataclass
class ProjectKnowledge:
    """Container for all project knowledge extracted from input files."""
    tickets: List[Dict]
    enriched_tickets: List[Dict]
    project_structure: List[str]
    git_commits: List[str]
    
    # Derived knowledge
    common_patterns: Dict[str, List[str]]
    file_change_patterns: Dict[str, List[str]]
    business_domains: Set[str]
    technical_components: Set[str]


@dataclass
class RefinedIdea:
    """Container for a refined idea with all generated content."""
    original_idea: str
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    related_tickets: List[str]


class KnowledgeExtractor:
    """Extracts and analyzes knowledge from project files."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_knowledge(self, jira_path: Path, enriched_path: Path, 
                         tree_path: Path, git_path: Path) -> ProjectKnowledge:
        """Extract all knowledge from input files."""
        self.logger.info("Extracting project knowledge...")
        
        # Load raw data
        tickets = self._load_json(jira_path)
        enriched_tickets = self._load_json(enriched_path)
        project_structure = self._load_text_lines(tree_path)
        git_commits = self._load_text_lines(git_path)
        
        # Analyze patterns
        common_patterns = self._extract_ticket_patterns(tickets)
        file_change_patterns = self._extract_file_patterns(enriched_tickets)
        business_domains = self._extract_business_domains(tickets)
        technical_components = self._extract_technical_components(project_structure)
        
        return ProjectKnowledge(
            tickets=tickets,
            enriched_tickets=enriched_tickets,
            project_structure=project_structure,
            git_commits=git_commits,
            common_patterns=common_patterns,
            file_change_patterns=file_change_patterns,
            business_domains=business_domains,
            technical_components=technical_components
        )
    
    def _load_json(self, path: Path) -> List[Dict]:
        """Load JSON file safely."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []
    
    def _load_text_lines(self, path: Path) -> List[str]:
        """Load text file as lines."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f if line.strip()]
        except Exception as e:
            self.logger.error(f"Failed to load {path}: {e}")
            return []
    
    def _extract_ticket_patterns(self, tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract common patterns from ticket descriptions."""
        patterns = defaultdict(list)
        
        for ticket in tickets:
            desc = ticket.get('description', '')
            title = ticket.get('title', '')
            
            # Extract business value patterns
            if '*Business Value:*' in desc:
                bv_match = re.search(r'\*Business Value:\*\s*([^*]+)', desc)
                if bv_match:
                    patterns['business_value'].append(bv_match.group(1).strip())
            
            # Extract acceptance criteria patterns
            if '*Acceptance Criteria:*' in desc:
                ac_match = re.search(r'\*Acceptance Criteria:\*\s*([^*]+)', desc)
                if ac_match:
                    patterns['acceptance_criteria'].append(ac_match.group(1).strip())
            
            # Extract impact patterns
            if '*Impact and notifications:*' in desc:
                impact_match = re.search(r'\*Impact and notifications:\*\s*([^*]+)', desc)
                if impact_match:
                    patterns['impact'].append(impact_match.group(1).strip())
        
        return dict(patterns)
    
    def _extract_file_patterns(self, enriched_tickets: List[Dict]) -> Dict[str, List[str]]:
        """Extract file change patterns by ticket type."""
        patterns = defaultdict(list)
        
        for ticket in enriched_tickets:
            title = ticket.get('title', '').lower()
            files_changed = ticket.get('files_changed', [])
            
            if not files_changed:
                continue
            
            # Categorize by ticket type
            if any(word in title for word in ['auth', 'authorization']):
                patterns['auth'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['clearing', 'settlement']):
                patterns['clearing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['api', 'endpoint']):
                patterns['api'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['test', 'testing']):
                patterns['testing'].extend([f['file_path'] for f in files_changed])
            elif any(word in title for word in ['config', 'configuration']):
                patterns['config'].extend([f['file_path'] for f in files_changed])
        
        return dict(patterns)
    
    def _extract_business_domains(self, tickets: List[Dict]) -> Set[str]:
        """Extract business domains from ticket content."""
        domains = set()
        
        for ticket in tickets:
            title = ticket.get('title', '').lower()
            desc = ticket.get('description', '').lower()
            content = f"{title} {desc}"
            
            # Common business domains in payment processing
            if any(word in content for word in ['visa', 'mastercard', 'mc']):
                domains.add('card_schemes')
            if any(word in content for word in ['amex', 'american express']):
                domains.add('amex')
            if any(word in content for word in ['auth', 'authorization']):
                domains.add('authorization')
            if any(word in content for word in ['clearing', 'settlement']):
                domains.add('clearing')
            if any(word in content for word in ['fraud', 'risk']):
                domains.add('fraud_prevention')
            if any(word in content for word in ['terminal', 'pos']):
                domains.add('terminal_management')
            if any(word in content for word in ['merchant', 'customer']):
                domains.add('merchant_services')
        
        return domains
    
    def _extract_technical_components(self, project_structure: List[str]) -> Set[str]:
        """Extract technical components from project structure."""
        components = set()
        
        for line in project_structure:
            if 'auth-' in line:
                components.add('auth_services')
            if 'clearing-' in line:
                components.add('clearing_services')
            if 'helm' in line:
                components.add('kubernetes')
            if 'config' in line:
                components.add('configuration')
            if 'test' in line:
                components.add('testing')
            if 'internal/' in line:
                components.add('internal_apis')
            if 'cmd/' in line:
                components.add('applications')
        
        return components


class LLMRefiner:
    """Uses LLM to refine ideas into comprehensive ticket descriptions."""
    
    def __init__(self, model_preset: str = "qwen3"):
        self.logger = logging.getLogger(__name__)
        self.model_preset = model_preset
        
        if not MLX_AVAILABLE:
            self.logger.warning("MLX not available - using template-based refinement")
            self.model = None
            self.tokenizer = None
            return
        
        try:
            model_path = PRESETS[model_preset]
            self.logger.info(f"Loading model from {model_path}")
            self.model, self.tokenizer = load(model_path)
            self.logger.info("Model loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            self.model = None
            self.tokenizer = None
    
    def refine_idea(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Refine a single idea into a comprehensive ticket description."""
        self.logger.info(f"Refining idea: {idea}")
        
        if self.model is None:
            return self._template_based_refinement(idea, knowledge)
        else:
            return self._llm_based_refinement(idea, knowledge)
    
    def _template_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """Fallback template-based refinement when LLM is not available."""
        self.logger.info("Using template-based refinement")
        
        # Generate title
        title = idea.strip().rstrip('.')
        if len(title) > 80:
            title = title[:77] + "..."
        
        # Analyze idea for domain and components
        idea_lower = idea.lower()
        relevant_domain = self._identify_domain(idea_lower, knowledge.business_domains)
        relevant_components = self._identify_components(idea_lower, knowledge.technical_components)
        
        # Generate sections
        business_value = self._generate_business_value(idea, relevant_domain)
        acceptance_criteria = self._generate_acceptance_criteria(idea, relevant_domain)
        open_questions = self._generate_open_questions(idea, relevant_domain)
        suggested_design = self._generate_design_suggestions(idea, relevant_components)
        likely_files = self._predict_file_changes(idea, knowledge.file_change_patterns)
        impact_assessment = self._generate_impact_assessment(idea, relevant_components)
        related_tickets = self._find_related_tickets(idea, knowledge.tickets)
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions, 
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=likely_files,
            impact_assessment=impact_assessment,
            related_tickets=related_tickets
        )
    
    def _llm_based_refinement(self, idea: str, knowledge: ProjectKnowledge) -> RefinedIdea:
        """LLM-based refinement using the loaded model."""
        self.logger.info("Using LLM-based refinement")
        
        # Build context from knowledge
        context = self._build_llm_context(knowledge)
        
        # Create prompt
        prompt = self._create_refinement_prompt(idea, context)
        
        try:
            # Generate response
            sampler = make_sampler(temp=0.3, top_p=0.9)
            response = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=2048,
                sampler=sampler,
                verbose=False
            )
            
            # Parse LLM response
            return self._parse_llm_response(idea, response)
            
        except Exception as e:
            self.logger.error(f"LLM refinement failed: {e}")
            return self._template_based_refinement(idea, knowledge)
    
    def _build_llm_context(self, knowledge: ProjectKnowledge) -> str:
        """Build context string for LLM from project knowledge."""
        context_parts = []
        
        # Add sample ticket patterns
        if knowledge.common_patterns.get('business_value'):
            context_parts.append("Example Business Values:")
            for bv in knowledge.common_patterns['business_value'][:3]:
                context_parts.append(f"- {bv}")
        
        # Add technical components
        if knowledge.technical_components:
            context_parts.append(f"\nTechnical Components: {', '.join(list(knowledge.technical_components)[:10])}")
        
        # Add business domains
        if knowledge.business_domains:
            context_parts.append(f"Business Domains: {', '.join(list(knowledge.business_domains))}")
        
        return "\n".join(context_parts)
    
    def _create_refinement_prompt(self, idea: str, context: str) -> str:
        """Create prompt for LLM refinement."""
        return f"""You are a senior technical product manager for a payment processing system. 
Your task is to refine a high-level idea into a comprehensive JIRA ticket description.

Project Context:
{context}

High-level idea to refine:
"{idea}"

Please provide a detailed ticket description with the following sections:
1. Title (concise, actionable)
2. Business Value (why this matters)
3. Acceptance Criteria (specific, testable requirements)
4. Open Questions (technical and business questions to resolve)
5. Suggested Design (high-level technical approach)
6. Impact Assessment (risks, dependencies, notifications needed)

Format your response as structured text with clear section headers.
"""
    
    def _parse_llm_response(self, idea: str, response: str) -> RefinedIdea:
        """Parse LLM response into RefinedIdea structure."""
        # This is a simplified parser - in practice you'd want more robust parsing
        sections = {}
        current_section = None
        current_content = []
        
        for line in response.split('\n'):
            line = line.strip()
            if not line:
                continue
                
            # Check for section headers
            if any(header in line.lower() for header in ['title:', 'business value:', 'acceptance criteria:', 'open questions:', 'suggested design:', 'impact assessment:']):
                if current_section:
                    sections[current_section] = '\n'.join(current_content)
                current_section = line.lower().split(':')[0].strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Add final section
        if current_section:
            sections[current_section] = '\n'.join(current_content)
        
        # Extract structured data
        title = sections.get('title', idea)
        business_value = sections.get('business value', 'To be determined')
        acceptance_criteria = [sections.get('acceptance criteria', 'To be defined')]
        open_questions = [sections.get('open questions', 'To be identified')]
        suggested_design = sections.get('suggested design', 'To be designed')
        impact_assessment = sections.get('impact assessment', 'To be assessed')
        
        description = self._build_description(
            business_value, acceptance_criteria, open_questions,
            suggested_design, impact_assessment
        )
        
        return RefinedIdea(
            original_idea=idea,
            title=title,
            business_value=business_value,
            description=description,
            acceptance_criteria=acceptance_criteria,
            open_questions=open_questions,
            suggested_design=suggested_design,
            likely_files=[],  # Would need additional analysis
            impact_assessment=impact_assessment,
            related_tickets=[]  # Would need additional analysis
        )
    
    def _identify_domain(self, idea_lower: str, domains: Set[str]) -> str:
        """Identify the most relevant business domain for the idea."""
        domain_keywords = {
            'authorization': ['auth', 'authorization', 'approve', 'decline'],
            'clearing': ['clearing', 'settlement', 'reconcile'],
            'fraud_prevention': ['fraud', 'risk', 'security', 'detect'],
            'card_schemes': ['visa', 'mastercard', 'mc', 'scheme'],
            'merchant_services': ['merchant', 'customer', 'dashboard'],
            'terminal_management': ['terminal', 'pos', 'device']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in idea_lower for keyword in keywords):
                return domain
        
        return 'general'
